<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\AppController;
use App\Http\Controllers\Api\CategoryController;
use App\Http\Controllers\Admin\DashboardController;

// Public routes
Route::post('/register', [AuthController::class, 'register']);
Route::post('/login', [AuthController::class, 'login']);

// Public app routes
Route::get('/apps', [AppController::class, 'index']);
Route::get('/apps/{id}', [AppController::class, 'show']);
Route::get('/apps/featured/list', [AppController::class, 'featured']);
Route::get('/apps/top-rated/list', [AppController::class, 'topRated']);
Route::get('/apps/most-downloaded/list', [AppController::class, 'mostDownloaded']);

// Public category routes
Route::get('/categories', [CategoryController::class, 'index']);
Route::get('/categories/{id}', [CategoryController::class, 'show']);

// Public stats route
Route::get('/stats', [AppController::class, 'stats']);

// Debug route to test connectivity
Route::get('/test', function () {
    return response()->json([
        'success' => true,
        'message' => 'API is working!',
        'timestamp' => now(),
    ]);
});

// Protected routes
Route::middleware('auth:sanctum')->group(function () {
    // Auth routes
    Route::post('/logout', [AuthController::class, 'logout']);
    Route::get('/profile', [AuthController::class, 'profile']);
    Route::put('/profile', [AuthController::class, 'updateProfile']);
    
    // User app management
    Route::get('/my-apps', [AppController::class, 'myApps']);
    Route::post('/apps', [AppController::class, 'store']);
    Route::put('/apps/{id}', [AppController::class, 'update']);
    Route::delete('/apps/{id}', [AppController::class, 'destroy']);
    
    // Admin routes
    Route::prefix('admin')->group(function () {
        Route::get('/stats', [DashboardController::class, 'stats']);
        Route::get('/users', [DashboardController::class, 'users']);
        Route::get('/apps', [DashboardController::class, 'apps']);
        Route::post('/apps/{id}/approve', [DashboardController::class, 'approveApp']);
        Route::post('/apps/{id}/reject', [DashboardController::class, 'rejectApp']);
        Route::post('/apps/{id}/toggle-feature', [DashboardController::class, 'toggleFeatureApp']);
        Route::post('/users/{id}/toggle-verify', [DashboardController::class, 'toggleVerifyUser']);
        Route::delete('/users/{id}', [DashboardController::class, 'deleteUser']);
        
        // Category management
        Route::post('/categories', [CategoryController::class, 'store']);
        Route::put('/categories/{id}', [CategoryController::class, 'update']);
        Route::delete('/categories/{id}', [CategoryController::class, 'destroy']);
    });
});
