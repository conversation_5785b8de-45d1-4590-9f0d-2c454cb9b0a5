version: "3.8"

services:
  noujoum_server:
    container_name: 'noujoum_server'
    build: .
    volumes:
      - './:/var/www/html'
      - '/var/www/html/vendor'
    ports:
      - "8011:80"
    depends_on:
      - noujoum_db
    restart: always
  noujoum_db:
    image: postgis/postgis:11-2.5
    container_name: 'noujoum_db'
    restart: always
    ports:
      - "5411:5432"
    environment:
      POSTGRES_PASSWORD: adminnoujoum
  noujoumPgadmin:
    image: dpage/pgadmin4
    container_name: 'noujoumPgadmin'
    restart: always
    ports:
      - "5011:80"
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: adminnoujoum
    depends_on:
      - noujoum_db

