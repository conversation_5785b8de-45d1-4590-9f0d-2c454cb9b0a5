<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Lara<PERSON>\Sanctum\HasApiTokens;

class App extends Model
{
    use HasFactory, HasApiTokens;

    protected $fillable = [
        'app_name',
        'tagline',
        'description',
        'detailed_description',
        'user_id',
        'developer_name',
        'developer_email',
        'developer_phone',
        'developer_whatsapp',
        'company_name',
        'developer_website',
        'app_type',
        'supported_platforms',
        'current_version',
        'icon_url',
        'screenshots',
        'demo_videos',
        'live_demo',
        'download_link',
        'license_type',
        'pricing_model',
        'pricing',
        'has_free_trial',
        'trial_days',
        'is_open_source',
        'target_audience',
        'business_sectors',
        'business_value',
        'key_features',
        'technical_requirements',
        'has_documentation',
        'documentation_url',
        'support_options',
        'languages',
        'downloads',
        'active_users',
        'rating',
        'view_count',
        'publish_date',
        'is_verified',
        'is_featured',
        'is_approved',
        'category_id',
        'subcategory',
        'tags',
        'search_keywords',
    ];

    protected $casts = [
        'supported_platforms' => 'array',
        'screenshots' => 'array',
        'demo_videos' => 'array',
        'business_sectors' => 'array',
        'key_features' => 'array',
        'support_options' => 'array',
        'languages' => 'array',
        'tags' => 'array',
        'has_free_trial' => 'boolean',
        'is_open_source' => 'boolean',
        'has_documentation' => 'boolean',
        'is_verified' => 'boolean',
        'is_featured' => 'boolean',
        'is_approved' => 'boolean',
        'publish_date' => 'datetime',
        'rating' => 'decimal:1',
    ];

    /**
     * Get the user that owns the app.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the category that owns the app.
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }

    /**
     * Scope a query to only include approved apps.
     */
    public function scopeApproved($query)
    {
        return $query->where('is_approved', true);
    }

    /**
     * Scope a query to only include featured apps.
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope a query to only include verified apps.
     */
    public function scopeVerified($query)
    {
        return $query->where('is_verified', true);
    }

    /**
     * Search apps by query.
     */
    public function scopeSearch($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('app_name', 'like', "%{$search}%")
              ->orWhere('description', 'like', "%{$search}%")
              ->orWhere('developer_name', 'like', "%{$search}%")
              ->orWhere('tags', 'like', "%{$search}%");
        });
    }

    /**
     * Get apps by category.
     */
    public function scopeByCategory($query, $categoryId)
    {
        return $query->where('category_id', $categoryId);
    }
}
