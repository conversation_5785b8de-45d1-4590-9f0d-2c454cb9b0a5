<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Category extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'name_arabic',
        'description',
        'icon',
        'color',
        'subcategories',
        'is_active',
    ];

    protected $casts = [
        'subcategories' => 'array',
        'is_active' => 'boolean',
    ];

    /**
     * Get the apps for the category.
     */
    public function apps(): HasMany
    {
        return $this->hasMany(App::class);
    }

    /**
     * Scope a query to only include active categories.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }
}
