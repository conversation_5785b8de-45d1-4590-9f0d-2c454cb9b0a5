<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\App;
use App\Models\User;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;

class DashboardController extends Controller
{
    public function __construct()
    {
        $this->middleware(function ($request, $next) {
            if (!$request->user()->isAdmin()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Admin access required'
                ], 403);
            }
            return $next($request);
        });
    }

    /**
     * Get dashboard statistics
     */
    public function stats(): JsonResponse
    {
        $stats = [
            'total_users' => User::count(),
            'verified_users' => User::verified()->count(),
            'total_apps' => App::count(),
            'approved_apps' => App::approved()->count(),
            'pending_apps' => App::where('is_approved', false)->count(),
            'featured_apps' => App::featured()->count(),
            'total_downloads' => App::sum('downloads'),
            'total_active_users' => App::sum('active_users'),
            'categories_count' => Category::active()->count(),
        ];

        return response()->json([
            'success' => true,
            'data' => $stats
        ]);
    }

    /**
     * Get all users with pagination
     */
    public function users(Request $request): JsonResponse
    {
        $query = User::withCount('apps');

        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('company_name', 'like', "%{$search}%");
            });
        }

        if ($request->has('role')) {
            $query->where('role', $request->role);
        }

        if ($request->has('verified')) {
            $query->where('is_verified', $request->boolean('verified'));
        }

        $users = $query->orderBy('created_at', 'desc')
            ->paginate($request->get('per_page', 20));

        return response()->json([
            'success' => true,
            'data' => $users
        ]);
    }

    /**
     * Get all apps for admin management
     */
    public function apps(Request $request): JsonResponse
    {
        $query = App::with(['user', 'category']);

        if ($request->has('search')) {
            $query->search($request->search);
        }

        if ($request->has('category')) {
            $query->byCategory($request->category);
        }

        if ($request->has('status')) {
            switch ($request->status) {
                case 'approved':
                    $query->approved();
                    break;
                case 'pending':
                    $query->where('is_approved', false);
                    break;
                case 'featured':
                    $query->featured();
                    break;
                case 'verified':
                    $query->verified();
                    break;
            }
        }

        $apps = $query->orderBy('created_at', 'desc')
            ->paginate($request->get('per_page', 20));

        return response()->json([
            'success' => true,
            'data' => $apps
        ]);
    }

    /**
     * Approve an app
     */
    public function approveApp(Request $request, string $id): JsonResponse
    {
        $app = App::findOrFail($id);
        $app->update(['is_approved' => true]);

        return response()->json([
            'success' => true,
            'message' => 'App approved successfully',
            'data' => $app
        ]);
    }

    /**
     * Reject an app
     */
    public function rejectApp(Request $request, string $id): JsonResponse
    {
        $app = App::findOrFail($id);
        $app->update(['is_approved' => false]);

        return response()->json([
            'success' => true,
            'message' => 'App rejected',
            'data' => $app
        ]);
    }

    /**
     * Feature/unfeature an app
     */
    public function toggleFeatureApp(Request $request, string $id): JsonResponse
    {
        $app = App::findOrFail($id);
        $app->update(['is_featured' => !$app->is_featured]);

        return response()->json([
            'success' => true,
            'message' => $app->is_featured ? 'App featured' : 'App unfeatured',
            'data' => $app
        ]);
    }

    /**
     * Verify/unverify a user
     */
    public function toggleVerifyUser(Request $request, string $id): JsonResponse
    {
        $user = User::findOrFail($id);
        $user->update(['is_verified' => !$user->is_verified]);

        return response()->json([
            'success' => true,
            'message' => $user->is_verified ? 'User verified' : 'User unverified',
            'data' => $user
        ]);
    }

    /**
     * Delete a user and their apps
     */
    public function deleteUser(Request $request, string $id): JsonResponse
    {
        $user = User::findOrFail($id);
        
        if ($user->isAdmin()) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot delete admin user'
            ], 400);
        }

        $user->delete();

        return response()->json([
            'success' => true,
            'message' => 'User deleted successfully'
        ]);
    }
}
