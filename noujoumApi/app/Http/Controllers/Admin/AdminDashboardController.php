<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\App;
use App\Models\Category;
use Illuminate\Support\Facades\DB;

class AdminDashboardController extends Controller
{
    public function __construct()
    {
        // $this->middleware(function ($request, $next) {
        //     if (!auth()->user()->is_admin) {
        //         abort(403, 'Access denied. Admin privileges required.');
        //     }
        //     return $next($request);
        // });
    }

    public function index()
    {
        $stats = [
            'total_users' => User::count(),
            'total_apps' => App::count(),
            'pending_apps' => App::where('is_approved', false)->count(),
            'approved_apps' => App::where('is_approved', true)->count(),
            'verified_users' => User::where('is_verified', true)->count(),
            'total_categories' => Category::count(),
        ];

        $recent_apps = App::with(['user', 'category'])
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        $recent_users = User::orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        return view('admin.dashboard', compact('stats', 'recent_apps', 'recent_users'));
    }

    public function users()
    {
        $users = User::with('apps')
            ->withCount('apps')
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return view('admin.users.index', compact('users'));
    }

    public function showUser($id)
    {
        $user = User::with(['apps' => function($query) {
            $query->orderBy('created_at', 'desc');
        }])->findOrFail($id);

        return view('admin.users.show', compact('user'));
    }

    public function verifyUser($id)
    {
        $user = User::findOrFail($id);
        $user->update(['is_verified' => true]);

        return back()->with('success', 'User verified successfully.');
    }

    public function unverifyUser($id)
    {
        $user = User::findOrFail($id);
        $user->update(['is_verified' => false]);

        return back()->with('success', 'User verification removed.');
    }

    public function apps()
    {
        $apps = App::with(['user', 'category'])
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return view('admin.apps.index', compact('apps'));
    }

    public function showApp($id)
    {
        $app = App::with(['user', 'category'])->findOrFail($id);
        return view('admin.apps.show', compact('app'));
    }

    public function approveApp($id)
    {
        $app = App::findOrFail($id);
        $app->update(['is_approved' => true]);

        return back()->with('success', 'App approved successfully.');
    }

    public function rejectApp($id)
    {
        $app = App::findOrFail($id);
        $app->update(['is_approved' => false]);

        return back()->with('success', 'App rejected.');
    }

    public function featureApp($id)
    {
        $app = App::findOrFail($id);
        $app->update(['is_featured' => true]);

        return back()->with('success', 'App featured successfully.');
    }

    public function unfeatureApp($id)
    {
        $app = App::findOrFail($id);
        $app->update(['is_featured' => false]);

        return back()->with('success', 'App unfeatured.');
    }

    public function deleteApp($id)
    {
        $app = App::findOrFail($id);
        $app->delete();

        return back()->with('success', 'App deleted successfully.');
    }
}
