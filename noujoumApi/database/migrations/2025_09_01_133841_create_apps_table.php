<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('apps', function (Blueprint $table) {
            $table->id();
            $table->string('app_name');
            $table->string('tagline');
            $table->text('description');
            $table->text('detailed_description');
            
            // Developer/Publisher Info
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('developer_name');
            $table->string('developer_email');
            $table->string('developer_phone');
            $table->string('developer_whatsapp')->nullable();
            $table->string('company_name')->nullable();
            $table->string('developer_website')->nullable();
            
            // App Technical Details
            $table->enum('app_type', ['mobile', 'web', 'desktop', 'saas', 'api', 'plugin', 'template']);
            $table->json('supported_platforms');
            $table->string('current_version')->default('1.0.0');
            $table->string('icon_url');
            $table->json('screenshots');
            $table->json('demo_videos')->nullable();
            $table->string('live_demo')->nullable();
            $table->string('download_link')->nullable();
            
            // Licensing & Pricing
            $table->enum('license_type', ['oneTime', 'monthly', 'yearly', 'custom', 'free']);
            $table->enum('pricing_model', ['free', 'freemium', 'paid', 'enterprise', 'custom']);
            $table->string('pricing');
            $table->boolean('has_free_trial')->default(false);
            $table->integer('trial_days')->default(0);
            $table->boolean('is_open_source')->default(false);
            
            // Business Information
            $table->string('target_audience');
            $table->json('business_sectors');
            $table->text('business_value');
            $table->json('key_features');
            $table->text('technical_requirements')->nullable();
            
            // Support & Documentation
            $table->boolean('has_documentation')->default(false);
            $table->string('documentation_url')->nullable();
            $table->json('support_options');
            $table->json('languages');
            
            // Marketplace Metrics
            $table->integer('downloads')->default(0);
            $table->integer('active_users')->default(0);
            $table->decimal('rating', 2, 1)->default(0.0);
            $table->integer('view_count')->default(0);
            $table->timestamp('publish_date')->nullable();
            $table->boolean('is_verified')->default(false);
            $table->boolean('is_featured')->default(false);
            $table->boolean('is_approved')->default(false);
            
            // Categories & Discovery
            $table->foreignId('category_id')->constrained()->onDelete('cascade');
            $table->string('subcategory');
            $table->json('tags');
            $table->text('search_keywords')->nullable();
            
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('apps');
    }
};
