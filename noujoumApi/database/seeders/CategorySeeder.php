<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Category;

class CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            [
                'name' => 'Santé',
                'name_arabic' => 'الصحة',
                'description' => 'Applications médicales et de santé',
                'icon' => 'local_hospital',
                'color' => '#f44336',
                'subcategories' => ['Médecine', 'Pharmacie', 'Fitness', 'Nutrition', 'Télémédecine'],
                'is_active' => true,
            ],
            [
                'name' => 'Gestion d\'Entreprise',
                'name_arabic' => 'إدارة الأعمال',
                'description' => 'Solutions de gestion pour PME mauritaniennes',
                'icon' => 'business_center',
                'color' => '#009688',
                'subcategories' => ['Gestion des stocks', 'Systèmes CRM', 'Logiciels de comptabilité', 'Gestion de projet', 'Gestion RH', 'Systèmes POS'],
                'is_active' => true,
            ],
            [
                'name' => 'Éducation',
                'name_arabic' => 'التعليم',
                'description' => 'Plateformes d\'apprentissage et éducatives',
                'icon' => 'school',
                'color' => '#2196f3',
                'subcategories' => ['E-learning', 'Langues', 'Sciences', 'Histoire'],
                'is_active' => true,
            ],
            [
                'name' => 'Agriculture',
                'name_arabic' => 'الزراعة',
                'description' => 'Outils agricoles et d\'élevage',
                'icon' => 'agriculture',
                'color' => '#4caf50',
                'subcategories' => ['Élevage', 'Cultures', 'Météo', 'Marchés'],
                'is_active' => true,
            ],
            [
                'name' => 'Transport',
                'name_arabic' => 'النقل',
                'description' => 'Services de transport et logistique',
                'icon' => 'directions_car',
                'color' => '#ff9800',
                'subcategories' => ['Taxi', 'Livraison', 'Transport Public', 'Logistique'],
                'is_active' => true,
            ],
            [
                'name' => 'Finance',
                'name_arabic' => 'المالية',
                'description' => 'Services bancaires et financiers',
                'icon' => 'account_balance',
                'color' => '#3f51b5',
                'subcategories' => ['Banque Mobile', 'Budget', 'Investissement', 'Commerce'],
                'is_active' => true,
            ],
            [
                'name' => 'Services Publics',
                'name_arabic' => 'الخدمات العامة',
                'description' => 'Services gouvernementaux numériques',
                'icon' => 'account_balance_wallet',
                'color' => '#9c27b0',
                'subcategories' => ['Documents', 'Impôts', 'État Civil', 'Citoyenneté'],
                'is_active' => true,
            ],
            [
                'name' => 'Commerce & E-commerce',
                'name_arabic' => 'التجارة الإلكترونية',
                'description' => 'Plateformes de vente et gestion commerciale',
                'icon' => 'shopping_cart',
                'color' => '#ff5722',
                'subcategories' => ['Boutiques en ligne', 'Systèmes d\'inventaire', 'Gestion clientèle', 'Traitement des paiements', 'Suivi de livraison', 'Outils marketing'],
                'is_active' => true,
            ],
            [
                'name' => 'Actualités',
                'name_arabic' => 'الأخبار',
                'description' => 'Information et médias locaux',
                'icon' => 'newspaper',
                'color' => '#795548',
                'subcategories' => ['Actualités', 'Politique', 'Sport', 'Culture'],
                'is_active' => true,
            ],
            [
                'name' => 'Religion',
                'name_arabic' => 'الدين',
                'description' => 'Applications religieuses et spirituelles',
                'icon' => 'mosque',
                'color' => '#607d8b',
                'subcategories' => ['Islam', 'Coran', 'Prière', 'Récitation'],
                'is_active' => true,
            ],
            [
                'name' => 'Tourisme',
                'name_arabic' => 'السياحة',
                'description' => 'Guides touristiques et voyage',
                'icon' => 'flight',
                'color' => '#e91e63',
                'subcategories' => ['Guide Voyage', 'Culture', 'Patrimoine', 'Hôtels'],
                'is_active' => true,
            ],
            [
                'name' => 'Alimentation',
                'name_arabic' => 'الطعام',
                'description' => 'Livraison de nourriture et restauration',
                'icon' => 'restaurant',
                'color' => '#ff6f00',
                'subcategories' => ['Livraison', 'Restaurants', 'Cuisine locale', 'Recettes'],
                'is_active' => true,
            ],
        ];

        foreach ($categories as $category) {
            Category::create($category);
        }
    }
}
