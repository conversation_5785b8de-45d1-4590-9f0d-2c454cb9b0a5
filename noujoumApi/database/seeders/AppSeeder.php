<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\App;
use App\Models\User;
use App\Models\Category;

class AppSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $businessCategory = Category::where('name', 'Gestion d\'Entreprise')->first();
        $healthCategory = Category::where('name', 'Santé')->first();
        $educationCategory = Category::where('name', 'Éducation')->first();
        $agricultureCategory = Category::where('name', 'Agriculture')->first();
        $transportCategory = Category::where('name', 'Transport')->first();
        $financeCategory = Category::where('name', 'Finance')->first();
        $governmentCategory = Category::where('name', 'Services Publics')->first();
        $commerceCategory = Category::where('name', 'Commerce & E-commerce')->first();
        $newsCategory = Category::where('name', 'Actualités')->first();
        $religionCategory = Category::where('name', 'Religion')->first();
        $tourismCategory = Category::where('name', 'Tourisme')->first();
        $foodCategory = Category::where('name', 'Alimentation')->first();

        $ahmed = User::where('email', '<EMAIL>')->first();
        $fatima = User::where('email', '<EMAIL>')->first();
        $mohamed = User::where('email', '<EMAIL>')->first();
        $aisha = User::where('email', '<EMAIL>')->first();
        $omar = User::where('email', '<EMAIL>')->first();

        $apps = [
            [
                'app_name' => 'MauriCRM Pro',
                'tagline' => 'Système de gestion client professionnel',
                'description' => 'Solution CRM complète pour les entreprises mauritaniennes',
                'detailed_description' => 'MauriCRM Pro est une solution de gestion de la relation client spécialement conçue pour les entreprises mauritaniennes. Gérez vos contacts, suivez vos ventes, automatisez votre marketing et améliorez votre service client.',
                'user_id' => $ahmed->id,
                'developer_name' => 'TechSolutions Mauritanie',
                'developer_email' => '<EMAIL>',
                'developer_phone' => '+222 45 67 89 01',
                'company_name' => 'TechSolutions SARL',
                'developer_website' => 'https://techsolutions.mr',
                'app_type' => 'saas',
                'supported_platforms' => ['web', 'android', 'iOS'],
                'icon_url' => 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=150&h=150&fit=crop&crop=center',
                'screenshots' => [
                    'https://images.unsplash.com/photo-**********-bebda4e38f71?w=300&h=600&fit=crop',
                    'https://images.unsplash.com/photo-1573164713714-d95e436ab8d6?w=300&h=600&fit=crop',
                    'https://images.unsplash.com/photo-**********-0cfed4f6a45d?w=300&h=600&fit=crop',
                ],
                'license_type' => 'monthly',
                'pricing_model' => 'paid',
                'pricing' => '500 MRU/mois',
                'has_free_trial' => true,
                'trial_days' => 14,
                'target_audience' => 'PME et grandes entreprises',
                'business_sectors' => ['Commerce', 'Services', 'Industrie'],
                'business_value' => 'Améliore la gestion client et augmente les ventes de 30%',
                'key_features' => ['Gestion contacts', 'Suivi ventes', 'Rapports avancés', 'Intégration email'],
                'support_options' => ['email', 'phone', 'training'],
                'languages' => ['Français', 'Arabe', 'Hassaniya'],
                'downloads' => 2500,
                'active_users' => 1200,
                'rating' => 4.6,
                'publish_date' => now()->subDays(200),
                'is_verified' => true,
                'is_featured' => true,
                'is_approved' => true,
                'category_id' => $businessCategory->id,
                'subcategory' => 'Systèmes CRM',
                'tags' => ['crm', 'ventes', 'clients', 'business'],
            ],
            [
                'app_name' => 'Pharmacie Finder MR',
                'tagline' => 'Trouvez les pharmacies ouvertes',
                'description' => 'Trouvez rapidement les pharmacies ouvertes près de chez vous à Nouakchott et dans toute la Mauritanie.',
                'detailed_description' => 'Pharmacie Finder MR vous aide à localiser rapidement les pharmacies ouvertes dans votre région. Consultez les horaires, vérifiez la disponibilité des médicaments et obtenez des directions précises.',
                'user_id' => $fatima->id,
                'developer_name' => 'TechMed Mauritanie',
                'developer_email' => '<EMAIL>',
                'developer_phone' => '+222 45 67 89 02',
                'company_name' => 'TechMed SARL',
                'developer_website' => 'https://techmed.mr',
                'app_type' => 'mobile',
                'supported_platforms' => ['android', 'iOS'],
                'icon_url' => 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=150&h=150&fit=crop&crop=center',
                'screenshots' => [
                    'https://images.unsplash.com/photo-**********-5c350d0d3c56?w=300&h=600&fit=crop',
                    'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=300&h=600&fit=crop',
                    'https://images.unsplash.com/photo-1584820927498-cfe5211fd8bf?w=300&h=600&fit=crop',
                ],
                'license_type' => 'free',
                'pricing_model' => 'free',
                'pricing' => 'Gratuit',
                'has_free_trial' => false,
                'trial_days' => 0,
                'target_audience' => 'Grand public',
                'business_sectors' => ['Santé', 'Services publics'],
                'business_value' => 'Facilite l\'accès aux soins pharmaceutiques',
                'key_features' => ['Localisation pharmacies', 'Horaires d\'ouverture', 'Disponibilité médicaments', 'Navigation GPS'],
                'support_options' => ['email'],
                'languages' => ['Français', 'Arabe'],
                'downloads' => 8500,
                'active_users' => 6200,
                'rating' => 4.2,
                'publish_date' => now()->subDays(180),
                'is_verified' => true,
                'is_featured' => false,
                'is_approved' => true,
                'category_id' => $healthCategory->id,
                'subcategory' => 'Pharmacie',
                'tags' => ['pharmacie', 'médicaments', 'urgence', 'localisation'],
            ],
            [
                'app_name' => 'École Numérique MR',
                'tagline' => 'L\'école à portée de main',
                'description' => 'Plateforme d\'apprentissage en ligne pour les élèves mauritaniens. Cours de mathématiques, sciences, français et arabe adaptés au programme national.',
                'detailed_description' => 'École Numérique MR offre une plateforme complète d\'apprentissage en ligne avec des cours interactifs, des exercices pratiques et un suivi personnalisé pour tous les niveaux scolaires.',
                'user_id' => $mohamed->id,
                'developer_name' => 'EduTech Mauritanie',
                'developer_email' => '<EMAIL>',
                'developer_phone' => '+222 45 67 89 04',
                'company_name' => 'EduTech SARL',
                'developer_website' => 'https://edutech.mr',
                'app_type' => 'web',
                'supported_platforms' => ['web', 'android', 'iOS'],
                'icon_url' => 'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=150&h=150&fit=crop&crop=center',
                'screenshots' => [
                    'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=300&h=600&fit=crop',
                    'https://images.unsplash.com/photo-1454165804606-c3d57bc86b40?w=300&h=600&fit=crop',
                    'https://images.unsplash.com/photo-1606107557195-0e29a4b5b4aa?w=300&h=600&fit=crop',
                ],
                'license_type' => 'monthly',
                'pricing_model' => 'freemium',
                'pricing' => 'Gratuit avec abonnement premium',
                'has_free_trial' => true,
                'trial_days' => 30,
                'target_audience' => 'Élèves et enseignants',
                'business_sectors' => ['Éducation', 'Formation'],
                'business_value' => 'Améliore les résultats scolaires de 40%',
                'key_features' => ['Cours interactifs', 'Exercices adaptatifs', 'Suivi progrès', 'Programme national'],
                'support_options' => ['email', 'phone', 'chat'],
                'languages' => ['Français', 'Arabe'],
                'downloads' => 20000,
                'active_users' => 15000,
                'rating' => 4.4,
                'publish_date' => now()->subDays(150),
                'is_verified' => true,
                'is_featured' => true,
                'is_approved' => true,
                'category_id' => $educationCategory->id,
                'subcategory' => 'E-learning',
                'tags' => ['éducation', 'cours', 'élèves', 'programme'],
            ],
            [
                'app_name' => 'Chinguetti Banking',
                'tagline' => 'Votre banque dans votre poche',
                'description' => 'Solution bancaire mobile complète. Gérez vos comptes, effectuez des virements, payez vos factures et suivez vos dépenses.',
                'detailed_description' => 'Chinguetti Banking offre une expérience bancaire mobile complète avec sécurité renforcée, interface intuitive et services financiers avancés pour les Mauritaniens.',
                'user_id' => $aisha->id,
                'developer_name' => 'FinTech Mauritanie',
                'developer_email' => '<EMAIL>',
                'developer_phone' => '+222 45 67 89 08',
                'company_name' => 'FinTech Mauritanie SARL',
                'developer_website' => 'https://fintech.mr',
                'app_type' => 'mobile',
                'supported_platforms' => ['android', 'iOS'],
                'icon_url' => 'https://images.unsplash.com/photo-**********-824ae1b704d3?w=150&h=150&fit=crop&crop=center',
                'screenshots' => [
                    'https://images.unsplash.com/photo-**********-bebda4e38f71?w=300&h=600&fit=crop',
                    'https://images.unsplash.com/photo-**********-0cfed4f6a45d?w=300&h=600&fit=crop',
                    'https://images.unsplash.com/photo-1579621970563-ebec7560ff3e?w=300&h=600&fit=crop',
                ],
                'license_type' => 'free',
                'pricing_model' => 'free',
                'pricing' => 'Gratuit pour les clients',
                'has_free_trial' => false,
                'trial_days' => 0,
                'target_audience' => 'Clients bancaires',
                'business_sectors' => ['Finance', 'Services bancaires'],
                'business_value' => 'Digitalise les services bancaires mauritaniens',
                'key_features' => ['Gestion comptes', 'Virements instantanés', 'Paiement factures', 'Historique transactions'],
                'support_options' => ['phone', 'email', 'chat'],
                'languages' => ['Français', 'Arabe'],
                'downloads' => 35000,
                'active_users' => 28000,
                'rating' => 4.8,
                'publish_date' => now()->subDays(120),
                'is_verified' => true,
                'is_featured' => true,
                'is_approved' => true,
                'category_id' => $financeCategory->id,
                'subcategory' => 'Banque Mobile',
                'tags' => ['banque', 'mobile', 'virement', 'factures'],
            ],
            [
                'app_name' => 'Nouakchott Taxi',
                'tagline' => 'Votre taxi en un clic',
                'description' => 'Service de taxi à la demande pour Nouakchott. Réservez votre course, suivez votre chauffeur en temps réel et payez facilement.',
                'detailed_description' => 'Nouakchott Taxi révolutionne le transport urbain avec une plateforme moderne de réservation de taxis. Géolocalisation, paiement sécurisé et suivi en temps réel.',
                'user_id' => $omar->id,
                'developer_name' => 'Urban Mobility MR',
                'developer_email' => '<EMAIL>',
                'developer_phone' => '+222 45 67 89 07',
                'company_name' => 'Urban Mobility SARL',
                'developer_website' => 'https://urbanmobility.mr',
                'app_type' => 'mobile',
                'supported_platforms' => ['android', 'iOS'],
                'icon_url' => 'https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=150&h=150&fit=crop&crop=center',
                'screenshots' => [
                    'https://images.unsplash.com/photo-**********-fcd25c85cd64?w=300&h=600&fit=crop',
                    'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=300&h=600&fit=crop',
                    'https://images.unsplash.com/photo-**********-0cfed4f6a45d?w=300&h=600&fit=crop',
                ],
                'license_type' => 'free',
                'pricing_model' => 'enterprise',
                'pricing' => 'Commission sur les courses',
                'has_free_trial' => false,
                'trial_days' => 0,
                'target_audience' => 'Résidents de Nouakchott',
                'business_sectors' => ['Transport', 'Services urbains'],
                'business_value' => 'Améliore la mobilité urbaine de 50%',
                'key_features' => ['Réservation instantanée', 'Suivi temps réel', 'Paiement mobile', 'Évaluation chauffeurs'],
                'support_options' => ['phone', 'chat'],
                'languages' => ['Français', 'Arabe', 'Hassaniya'],
                'downloads' => 25000,
                'active_users' => 18000,
                'rating' => 4.1,
                'publish_date' => now()->subDays(90),
                'is_verified' => true,
                'is_featured' => true,
                'is_approved' => false, // Pending approval
                'category_id' => $transportCategory->id,
                'subcategory' => 'Taxi',
                'tags' => ['taxi', 'transport', 'nouakchott', 'réservation'],
            ],
        ];

        foreach ($apps as $appData) {
            App::create($appData);
        }
    }
}
