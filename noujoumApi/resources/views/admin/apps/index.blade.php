@extends('admin.layouts.app')

@section('title', 'Applications')

@section('content')
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-mobile-alt me-2"></i>
        Applications Management
    </h1>
</div>

<!-- Filter Tabs -->
<ul class="nav nav-tabs mb-4">
    <li class="nav-item">
        <a class="nav-link {{ !request('status') ? 'active' : '' }}" 
           href="{{ route('admin.apps') }}">
            All Apps ({{ $apps->total() }})
        </a>
    </li>
    <li class="nav-item">
        <a class="nav-link {{ request('status') == 'pending' ? 'active' : '' }}" 
           href="{{ route('admin.apps', ['status' => 'pending']) }}">
            Pending Approval
        </a>
    </li>
    <li class="nav-item">
        <a class="nav-link {{ request('status') == 'approved' ? 'active' : '' }}" 
           href="{{ route('admin.apps', ['status' => 'approved']) }}">
            Approved
        </a>
    </li>
    <li class="nav-item">
        <a class="nav-link {{ request('status') == 'featured' ? 'active' : '' }}" 
           href="{{ route('admin.apps', ['status' => 'featured']) }}">
            Featured
        </a>
    </li>
</ul>

<div class="card">
    <div class="card-body">
        @if($apps->count() > 0)
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>App</th>
                            <th>Developer</th>
                            <th>Category</th>
                            <th>Status</th>
                            <th>Downloads</th>
                            <th>Rating</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($apps as $app)
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    @if($app->icon_url)
                                        <img src="{{ $app->icon_url }}" alt="{{ $app->app_name }}" 
                                             class="rounded me-3" width="48" height="48">
                                    @endif
                                    <div>
                                        <strong>{{ $app->app_name }}</strong>
                                        <br>
                                        <small class="text-muted">{{ Str::limit($app->description, 60) }}</small>
                                        <br>
                                        <small class="text-info">{{ $app->tagline }}</small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div>
                                    <strong>{{ $app->user->name }}</strong>
                                    <br>
                                    <small class="text-muted">{{ $app->user->email }}</small>
                                    @if($app->user->is_verified)
                                        <br><span class="badge bg-success">Verified</span>
                                    @endif
                                </div>
                            </td>
                            <td>
                                @if($app->category)
                                    <span class="badge bg-secondary">{{ $app->category->name }}</span>
                                @endif
                                @if($app->subcategory)
                                    <br><small class="text-muted">{{ $app->subcategory }}</small>
                                @endif
                            </td>
                            <td>
                                @if($app->is_approved)
                                    <span class="badge bg-success">
                                        <i class="fas fa-check me-1"></i>Approved
                                    </span>
                                @else
                                    <span class="badge bg-warning">
                                        <i class="fas fa-clock me-1"></i>Pending
                                    </span>
                                @endif
                                
                                @if($app->is_featured)
                                    <br><span class="badge bg-primary">
                                        <i class="fas fa-star me-1"></i>Featured
                                    </span>
                                @endif
                                
                                @if($app->is_verified)
                                    <br><span class="badge bg-info">
                                        <i class="fas fa-shield-alt me-1"></i>Verified
                                    </span>
                                @endif
                            </td>
                            <td>
                                <strong>{{ number_format($app->downloads) }}</strong>
                                <br>
                                <small class="text-muted">{{ number_format($app->active_users) }} active</small>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <span class="me-1">{{ number_format($app->rating, 1) }}</span>
                                    <div class="text-warning">
                                        @for($i = 1; $i <= 5; $i++)
                                            @if($i <= $app->rating)
                                                <i class="fas fa-star"></i>
                                            @else
                                                <i class="far fa-star"></i>
                                            @endif
                                        @endfor
                                    </div>
                                </div>
                            </td>
                            <td>
                                {{ $app->created_at->format('M d, Y') }}
                                <br>
                                <small class="text-muted">{{ $app->created_at->diffForHumans() }}</small>
                            </td>
                            <td>
                                <div class="btn-group-vertical btn-group-sm">
                                    <a href="{{ route('admin.apps.show', $app->id) }}" 
                                       class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-eye"></i> View
                                    </a>
                                    
                                    @if(!$app->is_approved)
                                        <form method="POST" action="{{ route('admin.apps.approve', $app->id) }}" class="d-inline">
                                            @csrf
                                            <button type="submit" class="btn btn-success btn-sm">
                                                <i class="fas fa-check"></i> Approve
                                            </button>
                                        </form>
                                    @else
                                        <form method="POST" action="{{ route('admin.apps.reject', $app->id) }}" class="d-inline">
                                            @csrf
                                            <button type="submit" class="btn btn-warning btn-sm">
                                                <i class="fas fa-times"></i> Reject
                                            </button>
                                        </form>
                                    @endif
                                    
                                    @if(!$app->is_featured)
                                        <form method="POST" action="{{ route('admin.apps.feature', $app->id) }}" class="d-inline">
                                            @csrf
                                            <button type="submit" class="btn btn-primary btn-sm">
                                                <i class="fas fa-star"></i> Feature
                                            </button>
                                        </form>
                                    @else
                                        <form method="POST" action="{{ route('admin.apps.unfeature', $app->id) }}" class="d-inline">
                                            @csrf
                                            <button type="submit" class="btn btn-outline-primary btn-sm">
                                                <i class="far fa-star"></i> Unfeature
                                            </button>
                                        </form>
                                    @endif
                                </div>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <div class="d-flex justify-content-center mt-4">
                {{ $apps->links() }}
            </div>
        @else
            <div class="text-center py-5">
                <i class="fas fa-mobile-alt fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No applications found</h5>
                <p class="text-muted">Applications will appear here once users start submitting them.</p>
            </div>
        @endif
    </div>
</div>
@endsection
