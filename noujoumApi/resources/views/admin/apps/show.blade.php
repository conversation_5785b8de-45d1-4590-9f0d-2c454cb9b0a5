@extends('admin.layouts.app')

@section('title', 'App Details - ' . $app->app_name)

@section('content')
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-mobile-alt me-2"></i>
        {{ $app->app_name }}
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            @if(!$app->is_approved)
                <form method="POST" action="{{ route('admin.apps.approve', $app->id) }}" class="d-inline">
                    @csrf
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-check"></i> Approve App
                    </button>
                </form>
            @else
                <form method="POST" action="{{ route('admin.apps.reject', $app->id) }}" class="d-inline">
                    @csrf
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-times"></i> Reject App
                    </button>
                </form>
            @endif
            
            @if(!$app->is_featured)
                <form method="POST" action="{{ route('admin.apps.feature', $app->id) }}" class="d-inline">
                    @csrf
                    <button type="submit" class="btn btn-primary ms-2">
                        <i class="fas fa-star"></i> Feature
                    </button>
                </form>
            @else
                <form method="POST" action="{{ route('admin.apps.unfeature', $app->id) }}" class="d-inline">
                    @csrf
                    <button type="submit" class="btn btn-outline-primary ms-2">
                        <i class="far fa-star"></i> Unfeature
                    </button>
                </form>
            @endif
        </div>
        
        <form method="POST" action="{{ route('admin.apps.delete', $app->id) }}" class="d-inline"
              onsubmit="return confirm('Are you sure you want to delete this app? This action cannot be undone.')">
            @csrf
            @method('DELETE')
            <button type="submit" class="btn btn-danger">
                <i class="fas fa-trash"></i> Delete
            </button>
        </form>
    </div>
</div>

<div class="row">
    <!-- App Information -->
    <div class="col-lg-8">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    Application Information
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 text-center mb-3">
                        @if($app->icon_url)
                            <img src="{{ $app->icon_url }}" alt="{{ $app->app_name }}" 
                                 class="img-fluid rounded" style="max-width: 120px;">
                        @else
                            <div class="bg-secondary rounded d-flex align-items-center justify-content-center" 
                                 style="width: 120px; height: 120px; margin: 0 auto;">
                                <i class="fas fa-mobile-alt fa-3x text-white"></i>
                            </div>
                        @endif
                    </div>
                    <div class="col-md-9">
                        <h4>{{ $app->app_name }}</h4>
                        <p class="text-primary mb-2">{{ $app->tagline }}</p>
                        <p class="mb-3">{{ $app->description }}</p>
                        
                        <div class="row">
                            <div class="col-sm-6">
                                <strong>Category:</strong> 
                                @if($app->category)
                                    <span class="badge bg-secondary">{{ $app->category->name }}</span>
                                @endif
                                @if($app->subcategory)
                                    <br><small class="text-muted">{{ $app->subcategory }}</small>
                                @endif
                            </div>
                            <div class="col-sm-6">
                                <strong>Type:</strong> {{ ucfirst($app->app_type) }}<br>
                                <strong>Pricing:</strong> {{ $app->pricing }}
                            </div>
                        </div>
                    </div>
                </div>
                
                <hr>
                
                <div class="row">
                    <div class="col-md-12">
                        <h6>Detailed Description</h6>
                        <p>{{ $app->detailed_description }}</p>
                    </div>
                </div>
                
                @if($app->key_features && count($app->key_features) > 0)
                <hr>
                <div class="row">
                    <div class="col-md-12">
                        <h6>Key Features</h6>
                        <ul>
                            @foreach($app->key_features as $feature)
                                <li>{{ $feature }}</li>
                            @endforeach
                        </ul>
                    </div>
                </div>
                @endif
                
                @if($app->screenshots && count($app->screenshots) > 0)
                <hr>
                <div class="row">
                    <div class="col-md-12">
                        <h6>Screenshots</h6>
                        <div class="row">
                            @foreach($app->screenshots as $screenshot)
                                <div class="col-md-3 mb-3">
                                    <img src="{{ $screenshot }}" alt="Screenshot" class="img-fluid rounded">
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
                @endif
            </div>
        </div>
        
        <!-- Developer Information -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user me-2"></i>
                    Developer Information
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <strong>Name:</strong> {{ $app->developer_name }}<br>
                        <strong>Email:</strong> 
                        <a href="mailto:{{ $app->developer_email }}">{{ $app->developer_email }}</a><br>
                        @if($app->developer_phone)
                            <strong>Phone:</strong> {{ $app->developer_phone }}<br>
                        @endif
                        @if($app->company_name)
                            <strong>Company:</strong> {{ $app->company_name }}<br>
                        @endif
                    </div>
                    <div class="col-md-6">
                        @if($app->developer_website)
                            <strong>Website:</strong> 
                            <a href="{{ $app->developer_website }}" target="_blank">{{ $app->developer_website }}</a><br>
                        @endif
                        <strong>User Account:</strong> 
                        <a href="{{ route('admin.users.show', $app->user->id) }}">{{ $app->user->name }}</a>
                        @if($app->user->is_verified)
                            <span class="badge bg-success">Verified</span>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Status and Statistics -->
    <div class="col-lg-4">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    Status & Statistics
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <strong>Approval Status:</strong><br>
                    @if($app->is_approved)
                        <span class="badge bg-success">
                            <i class="fas fa-check me-1"></i>Approved
                        </span>
                    @else
                        <span class="badge bg-warning">
                            <i class="fas fa-clock me-1"></i>Pending Approval
                        </span>
                    @endif
                </div>
                
                <div class="mb-3">
                    <strong>Featured Status:</strong><br>
                    @if($app->is_featured)
                        <span class="badge bg-primary">
                            <i class="fas fa-star me-1"></i>Featured
                        </span>
                    @else
                        <span class="badge bg-secondary">Not Featured</span>
                    @endif
                </div>
                
                <div class="mb-3">
                    <strong>Verification:</strong><br>
                    @if($app->is_verified)
                        <span class="badge bg-info">
                            <i class="fas fa-shield-alt me-1"></i>Verified
                        </span>
                    @else
                        <span class="badge bg-secondary">Not Verified</span>
                    @endif
                </div>
                
                <hr>
                
                <div class="mb-3">
                    <strong>Downloads:</strong><br>
                    <span class="h5">{{ number_format($app->downloads) }}</span>
                </div>
                
                <div class="mb-3">
                    <strong>Active Users:</strong><br>
                    <span class="h6">{{ number_format($app->active_users) }}</span>
                </div>
                
                <div class="mb-3">
                    <strong>Rating:</strong><br>
                    <div class="d-flex align-items-center">
                        <span class="h6 me-2">{{ number_format($app->rating, 1) }}</span>
                        <div class="text-warning">
                            @for($i = 1; $i <= 5; $i++)
                                @if($i <= $app->rating)
                                    <i class="fas fa-star"></i>
                                @else
                                    <i class="far fa-star"></i>
                                @endif
                            @endfor
                        </div>
                    </div>
                </div>
                
                <hr>
                
                <div class="mb-3">
                    <strong>Created:</strong><br>
                    {{ $app->created_at->format('M d, Y H:i') }}<br>
                    <small class="text-muted">{{ $app->created_at->diffForHumans() }}</small>
                </div>
                
                <div class="mb-3">
                    <strong>Last Updated:</strong><br>
                    {{ $app->updated_at->format('M d, Y H:i') }}<br>
                    <small class="text-muted">{{ $app->updated_at->diffForHumans() }}</small>
                </div>
            </div>
        </div>
        
        <!-- Technical Details -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-cog me-2"></i>
                    Technical Details
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <strong>Platforms:</strong><br>
                    @if($app->supported_platforms && count($app->supported_platforms) > 0)
                        @foreach($app->supported_platforms as $platform)
                            <span class="badge bg-info me-1">{{ $platform }}</span>
                        @endforeach
                    @endif
                </div>
                
                <div class="mb-3">
                    <strong>Languages:</strong><br>
                    @if($app->languages && count($app->languages) > 0)
                        @foreach($app->languages as $language)
                            <span class="badge bg-secondary me-1">{{ $language }}</span>
                        @endforeach
                    @endif
                </div>
                
                @if($app->tags && count($app->tags) > 0)
                <div class="mb-3">
                    <strong>Tags:</strong><br>
                    @foreach($app->tags as $tag)
                        <span class="badge bg-light text-dark me-1">#{{ $tag }}</span>
                    @endforeach
                </div>
                @endif
                
                @if($app->business_sectors && count($app->business_sectors) > 0)
                <div class="mb-3">
                    <strong>Business Sectors:</strong><br>
                    @foreach($app->business_sectors as $sector)
                        <span class="badge bg-outline-primary me-1">{{ $sector }}</span>
                    @endforeach
                </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection
