@extends('admin.layouts.app')

@section('title', 'User Details - ' . $user->name)

@section('content')
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-user me-2"></i>
        {{ $user->name }}
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            @if(!$user->is_admin)
                @if(!$user->is_verified)
                    <form method="POST" action="{{ route('admin.users.verify', $user->id) }}" class="d-inline">
                        @csrf
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-check"></i> Verify User
                        </button>
                    </form>
                @else
                    <form method="POST" action="{{ route('admin.users.unverify', $user->id) }}" class="d-inline">
                        @csrf
                        <button type="submit" class="btn btn-warning">
                            <i class="fas fa-times"></i> Unverify User
                        </button>
                    </form>
                @endif
            @endif
        </div>
        
        <a href="{{ route('admin.users.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left"></i> Back to Users
        </a>
    </div>
</div>

<div class="row">
    <!-- User Profile Information -->
    <div class="col-lg-8">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-id-card me-2"></i>
                    Profile Information
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 text-center mb-3">
                        @if($user->avatar_url)
                            <img src="{{ $user->avatar_url }}" alt="{{ $user->name }}" 
                                 class="img-fluid rounded-circle" style="max-width: 150px;">
                        @else
                            <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center mx-auto" 
                                 style="width: 150px; height: 150px;">
                                <span class="text-white display-4 fw-bold">{{ substr($user->name, 0, 1) }}</span>
                            </div>
                        @endif
                    </div>
                    <div class="col-md-9">
                        <h4>{{ $user->name }}</h4>
                        @if($user->company_name)
                            <p class="text-primary mb-2">{{ $user->company_name }}</p>
                        @endif
                        @if($user->bio)
                            <p class="mb-3">{{ $user->bio }}</p>
                        @endif
                        
                        <div class="row">
                            <div class="col-sm-6">
                                <strong>Email:</strong> 
                                <a href="mailto:{{ $user->email }}">{{ $user->email }}</a>
                                @if($user->email_verified_at)
                                    <span class="badge bg-success ms-1">Verified</span>
                                @else
                                    <span class="badge bg-warning ms-1">Unverified</span>
                                @endif
                            </div>
                            <div class="col-sm-6">
                                @if($user->phone)
                                    <strong>Phone:</strong> {{ $user->phone }}<br>
                                @endif
                                @if($user->website)
                                    <strong>Website:</strong> 
                                    <a href="{{ $user->website }}" target="_blank">{{ $user->website }}</a>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
                
                @if($user->skills && count($user->skills) > 0)
                <hr>
                <div class="row">
                    <div class="col-md-12">
                        <h6>Skills</h6>
                        @foreach($user->skills as $skill)
                            <span class="badge bg-info me-1">{{ $skill }}</span>
                        @endforeach
                    </div>
                </div>
                @endif
                
                @if($user->interests && count($user->interests) > 0)
                <hr>
                <div class="row">
                    <div class="col-md-12">
                        <h6>Interests</h6>
                        @foreach($user->interests as $interest)
                            <span class="badge bg-secondary me-1">{{ $interest }}</span>
                        @endforeach
                    </div>
                </div>
                @endif
                
                @if($user->location)
                <hr>
                <div class="row">
                    <div class="col-md-12">
                        <h6>Location</h6>
                        <p><i class="fas fa-map-marker-alt me-2"></i>{{ $user->location }}</p>
                    </div>
                </div>
                @endif
            </div>
        </div>
        
        <!-- User's Apps -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-mobile-alt me-2"></i>
                    Published Apps ({{ $user->apps->count() }})
                </h5>
            </div>
            <div class="card-body">
                @if($user->apps->count() > 0)
                    <div class="row">
                        @foreach($user->apps as $app)
                            <div class="col-md-6 mb-3">
                                <div class="card h-100">
                                    <div class="card-body">
                                        <div class="d-flex align-items-start">
                                            @if($app->icon_url)
                                                <img src="{{ $app->icon_url }}" alt="{{ $app->app_name }}" 
                                                     class="rounded me-3" style="width: 48px; height: 48px;">
                                            @else
                                                <div class="bg-secondary rounded d-flex align-items-center justify-content-center me-3" 
                                                     style="width: 48px; height: 48px;">
                                                    <i class="fas fa-mobile-alt text-white"></i>
                                                </div>
                                            @endif
                                            <div class="flex-grow-1">
                                                <h6 class="card-title mb-1">{{ $app->app_name }}</h6>
                                                <p class="card-text small text-muted mb-2">{{ Str::limit($app->tagline, 60) }}</p>
                                                
                                                <div class="d-flex flex-wrap gap-1 mb-2">
                                                    @if($app->is_approved)
                                                        <span class="badge bg-success">Approved</span>
                                                    @else
                                                        <span class="badge bg-warning">Pending</span>
                                                    @endif
                                                    
                                                    @if($app->is_featured)
                                                        <span class="badge bg-primary">Featured</span>
                                                    @endif
                                                    
                                                    @if($app->is_verified)
                                                        <span class="badge bg-info">Verified</span>
                                                    @endif
                                                </div>
                                                
                                                <div class="small text-muted">
                                                    <i class="fas fa-download me-1"></i>{{ number_format($app->downloads) }} downloads
                                                    <i class="fas fa-star ms-2 me-1"></i>{{ number_format($app->rating, 1) }}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="card-footer bg-transparent">
                                        <a href="{{ route('admin.apps.show', $app->id) }}" class="btn btn-outline-primary btn-sm">
                                            <i class="fas fa-eye"></i> View Details
                                        </a>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-4">
                        <i class="fas fa-mobile-alt fa-3x text-muted mb-3"></i>
                        <h6 class="text-muted">No apps published yet</h6>
                        <p class="text-muted small">This user hasn't published any apps to the marketplace.</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
    
    <!-- Status and Statistics -->
    <div class="col-lg-4">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    Status & Statistics
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <strong>Account Type:</strong><br>
                    @if($user->is_admin)
                        <span class="badge bg-danger">
                            <i class="fas fa-crown me-1"></i>Administrator
                        </span>
                    @else
                        <span class="badge bg-primary">
                            <i class="fas fa-user me-1"></i>Regular User
                        </span>
                    @endif
                </div>
                
                <div class="mb-3">
                    <strong>Verification Status:</strong><br>
                    @if($user->is_verified)
                        <span class="badge bg-success">
                            <i class="fas fa-check-circle me-1"></i>Verified
                        </span>
                    @else
                        <span class="badge bg-warning">
                            <i class="fas fa-clock me-1"></i>Unverified
                        </span>
                    @endif
                </div>
                
                <div class="mb-3">
                    <strong>Email Status:</strong><br>
                    @if($user->email_verified_at)
                        <span class="badge bg-info">
                            <i class="fas fa-envelope-check me-1"></i>Email Verified
                        </span>
                        <br><small class="text-muted">{{ $user->email_verified_at->format('M d, Y') }}</small>
                    @else
                        <span class="badge bg-secondary">Email Not Verified</span>
                    @endif
                </div>
                
                <hr>
                
                <div class="mb-3">
                    <strong>Total Apps:</strong><br>
                    <span class="h5">{{ $user->apps->count() }}</span>
                </div>
                
                <div class="mb-3">
                    <strong>Approved Apps:</strong><br>
                    <span class="h6">{{ $user->apps->where('is_approved', true)->count() }}</span>
                </div>
                
                <div class="mb-3">
                    <strong>Pending Apps:</strong><br>
                    <span class="h6">{{ $user->apps->where('is_approved', false)->count() }}</span>
                </div>
                
                <div class="mb-3">
                    <strong>Featured Apps:</strong><br>
                    <span class="h6">{{ $user->apps->where('is_featured', true)->count() }}</span>
                </div>
                
                <hr>
                
                <div class="mb-3">
                    <strong>Total Downloads:</strong><br>
                    <span class="h6">{{ number_format($user->apps->sum('downloads')) }}</span>
                </div>
                
                <div class="mb-3">
                    <strong>Average Rating:</strong><br>
                    @php
                        $avgRating = $user->apps->avg('rating');
                    @endphp
                    @if($avgRating)
                        <div class="d-flex align-items-center">
                            <span class="h6 me-2">{{ number_format($avgRating, 1) }}</span>
                            <div class="text-warning">
                                @for($i = 1; $i <= 5; $i++)
                                    @if($i <= $avgRating)
                                        <i class="fas fa-star"></i>
                                    @else
                                        <i class="far fa-star"></i>
                                    @endif
                                @endfor
                            </div>
                        </div>
                    @else
                        <span class="text-muted">No ratings yet</span>
                    @endif
                </div>
                
                <hr>
                
                <div class="mb-3">
                    <strong>Member Since:</strong><br>
                    {{ $user->created_at->format('M d, Y') }}<br>
                    <small class="text-muted">{{ $user->created_at->diffForHumans() }}</small>
                </div>
                
                <div class="mb-3">
                    <strong>Last Updated:</strong><br>
                    {{ $user->updated_at->format('M d, Y H:i') }}<br>
                    <small class="text-muted">{{ $user->updated_at->diffForHumans() }}</small>
                </div>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="mailto:{{ $user->email }}" class="btn btn-outline-primary">
                        <i class="fas fa-envelope me-2"></i>Send Email
                    </a>
                    
                    @if($user->phone)
                        <a href="tel:{{ $user->phone }}" class="btn btn-outline-success">
                            <i class="fas fa-phone me-2"></i>Call User
                        </a>
                    @endif
                    
                    @if($user->website)
                        <a href="{{ $user->website }}" target="_blank" class="btn btn-outline-info">
                            <i class="fas fa-external-link-alt me-2"></i>Visit Website
                        </a>
                    @endif
                    
                    @if($user->apps->where('is_approved', false)->count() > 0)
                        <a href="{{ route('admin.apps.index', ['status' => 'pending', 'user' => $user->id]) }}" 
                           class="btn btn-outline-warning">
                            <i class="fas fa-clock me-2"></i>Review Pending Apps
                        </a>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
