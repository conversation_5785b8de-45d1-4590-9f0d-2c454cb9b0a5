@extends('admin.layouts.app')

@section('title', 'Users')

@section('content')
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-users me-2"></i>
        Users Management
    </h1>
</div>

<div class="card">
    <div class="card-body">
        @if($users->count() > 0)
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>User</th>
                            <th>Contact</th>
                            <th>Apps</th>
                            <th>Status</th>
                            <th>Joined</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($users as $user)
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    @if($user->avatar_url)
                                        <img src="{{ $user->avatar_url }}" alt="{{ $user->name }}" 
                                             class="rounded-circle me-3" width="48" height="48">
                                    @else
                                        <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center me-3" 
                                             style="width: 48px; height: 48px;">
                                            <span class="text-white fw-bold">{{ substr($user->name, 0, 1) }}</span>
                                        </div>
                                    @endif
                                    <div>
                                        <strong>{{ $user->name }}</strong>
                                        @if($user->company_name)
                                            <br><small class="text-muted">{{ $user->company_name }}</small>
                                        @endif
                                        @if($user->bio)
                                            <br><small class="text-info">{{ Str::limit($user->bio, 50) }}</small>
                                        @endif
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div>
                                    <strong>{{ $user->email }}</strong>
                                    @if($user->phone)
                                        <br><small class="text-muted">{{ $user->phone }}</small>
                                    @endif
                                    @if($user->website)
                                        <br><a href="{{ $user->website }}" target="_blank" class="small">
                                            <i class="fas fa-external-link-alt"></i> Website
                                        </a>
                                    @endif
                                </div>
                            </td>
                            <td>
                                <div class="text-center">
                                    <span class="badge bg-primary">{{ $user->apps_count }}</span>
                                    <br><small class="text-muted">apps</small>
                                </div>
                            </td>
                            <td>
                                @if($user->is_admin)
                                    <span class="badge bg-danger">
                                        <i class="fas fa-crown me-1"></i>Admin
                                    </span>
                                    <br>
                                @endif
                                
                                @if($user->is_verified)
                                    <span class="badge bg-success">
                                        <i class="fas fa-check-circle me-1"></i>Verified
                                    </span>
                                @else
                                    <span class="badge bg-warning">
                                        <i class="fas fa-clock me-1"></i>Unverified
                                    </span>
                                @endif
                                
                                @if($user->email_verified_at)
                                    <br><span class="badge bg-info">
                                        <i class="fas fa-envelope-check me-1"></i>Email Verified
                                    </span>
                                @endif
                            </td>
                            <td>
                                {{ $user->created_at->format('M d, Y') }}
                                <br>
                                <small class="text-muted">{{ $user->created_at->diffForHumans() }}</small>
                            </td>
                            <td>
                                <div class="btn-group-vertical btn-group-sm">
                                    <a href="{{ route('admin.users.show', $user->id) }}" 
                                       class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-eye"></i> View
                                    </a>
                                    
                                    @if(!$user->is_admin)
                                        @if(!$user->is_verified)
                                            <form method="POST" action="{{ route('admin.users.verify', $user->id) }}" class="d-inline">
                                                @csrf
                                                <button type="submit" class="btn btn-success btn-sm">
                                                    <i class="fas fa-check"></i> Verify
                                                </button>
                                            </form>
                                        @else
                                            <form method="POST" action="{{ route('admin.users.unverify', $user->id) }}" class="d-inline">
                                                @csrf
                                                <button type="submit" class="btn btn-warning btn-sm">
                                                    <i class="fas fa-times"></i> Unverify
                                                </button>
                                            </form>
                                        @endif
                                    @endif
                                </div>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <div class="d-flex justify-content-center mt-4">
                {{ $users->links() }}
            </div>
        @else
            <div class="text-center py-5">
                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No users found</h5>
                <p class="text-muted">Users will appear here once they register.</p>
            </div>
        @endif
    </div>
</div>
@endsection
